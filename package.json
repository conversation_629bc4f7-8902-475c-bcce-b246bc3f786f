{"name": "video-upload-preview-app", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"ajv": "^8.17.1", "react": "18.3.1", "react-dom": "18.3.1", "react-router-dom": "6.30.0", "react-scripts": "5.0.1", "wavesurfer.js": "7.9.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/node": "^22.15.17", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "typescript": "^5.8.3"}}